import asyncio
import json
import logging
import os
from typing import List, Dict, Any
from datetime import datetime

from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.theme import Theme
from rich.progress import Progress, SpinnerColumn, TextColumn

from stagehand import Stagehand, StagehandConfig, configure_logging

# Configure logging
configure_logging(
    level=logging.INFO,
    remove_logger_name=True,
    quiet_dependencies=True,
)

# Create a custom theme for consistent styling
custom_theme = Theme(
    {
        "info": "cyan",
        "success": "green",
        "warning": "yellow",
        "error": "red bold",
        "highlight": "magenta",
        "url": "blue underline",
        "bank": "bright_blue",
    }
)

console = Console(theme=custom_theme)
load_dotenv()

class CONDUSEFBanksScraper:
    def __init__(self):
        self.config = StagehandConfig(
            env="BROWSERBASE",
            api_key=os.getenv("BROWSERBASE_API_KEY"),
            project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
            headless=False,
            dom_settle_timeout_ms=5000,
            model_name="google/gemini-2.0-flash",
            self_heal=True,
            wait_for_captcha_solves=True,
            system_prompt="You are a web scraping assistant specialized in navigating financial institution directories. You help extract bank information from CONDUSEF's website.",
            model_client_options={"apiKey": os.getenv("MODEL_API_KEY")},
            verbose=2,
        )
        self.stagehand = None
        self.page = None
        self.banks_data = []

    async def init(self):
        """Initialize Stagehand and create a new session"""
        console.print("\n🚀 [info]Initializing CONDUSEF Banks Scraper...[/]")
        self.stagehand = Stagehand(self.config)
        await self.stagehand.init()
        self.page = self.stagehand.page
        console.print(f"\n[yellow]Created new session:[/] {self.stagehand.session_id}")
        console.print(
            f"🌐 [white]View your live browser:[/] [url]https://www.browserbase.com/sessions/{self.stagehand.session_id}[/]"
        )

    async def navigate_to_condusef(self):
        """Navigate to CONDUSEF SIPRES website"""
        console.print("\n▶️ [highlight]Navigating[/] to CONDUSEF SIPRES")
        await self.page.goto("https://webapps.condusef.gob.mx/SIPRES/jsp/pub/index.jsp")
        await asyncio.sleep(3)
        console.print("✅ [success]Navigated to CONDUSEF SIPRES[/]")

    async def find_banks_section(self):
        """Find and navigate to the banks section"""
        console.print("\n▶️ [highlight]Looking for[/] BANCOS - Instituciones de banca múltiple section")
        
        # Try to find the banks section
        try:
            # Look for the banks section in the page
            await self.page.act("find and click on 'BANCOS - Instituciones de banca múltiple' or similar banking institutions section")
            await asyncio.sleep(3)
            console.print("✅ [success]Found and clicked on banks section[/]")
            return True
        except Exception as e:
            console.print(f"❌ [error]Error finding banks section: {e}[/]")
            return False

    async def extract_banks_list(self):
        """Extract the list of banks and their websites"""
        console.print("\n▶️ [highlight]Extracting[/] banks information")
        
        try:
            # Extract all bank information including names and websites
            banks_info = await self.page.extract(
                "extract all banks listed with their names and website URLs. "
                "Return a list of objects with 'name' and 'website' fields for each bank."
            )
            
            if banks_info and hasattr(banks_info, 'model_dump'):
                self.banks_data = banks_info.model_dump()
            else:
                self.banks_data = banks_info
                
            console.print(f"✅ [success]Extracted information for {len(self.banks_data) if isinstance(self.banks_data, list) else 'unknown number of'} banks[/]")
            return True
            
        except Exception as e:
            console.print(f"❌ [error]Error extracting banks data: {e}[/]")
            return False

    async def scrape_individual_bank_pages(self):
        """Navigate to individual bank pages to get more detailed information"""
        console.print("\n▶️ [highlight]Scraping individual bank pages[/] for detailed information")
        
        detailed_banks = []
        
        # If we have a list of banks, try to get more details for each
        if isinstance(self.banks_data, list):
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Processing banks...", total=len(self.banks_data))
                
                for bank in self.banks_data:
                    try:
                        bank_name = bank.get('name', 'Unknown Bank')
                        progress.update(task, description=f"Processing {bank_name}")
                        
                        # Try to click on the bank to get more details
                        await self.page.act(f"click on {bank_name} to see more details")
                        await asyncio.sleep(2)
                        
                        # Extract detailed information
                        detailed_info = await self.page.extract(
                            f"extract detailed information for {bank_name} including website URL, contact information, and any other available details"
                        )
                        
                        if detailed_info:
                            if hasattr(detailed_info, 'model_dump'):
                                detailed_banks.append(detailed_info.model_dump())
                            else:
                                detailed_banks.append(detailed_info)
                        
                        # Go back to the list
                        await self.page.keyboard.press("Escape")
                        await asyncio.sleep(1)
                        
                        progress.advance(task)
                        
                    except Exception as e:
                        console.print(f"⚠️ [warning]Could not get details for {bank.get('name', 'Unknown')}: {e}[/]")
                        progress.advance(task)
                        continue
        
        if detailed_banks:
            self.banks_data = detailed_banks
            console.print(f"✅ [success]Collected detailed information for {len(detailed_banks)} banks[/]")

    def display_results(self):
        """Display the scraped results in a formatted table"""
        console.print("\n📊 [info]Banks Information Summary[/]")
        
        if not self.banks_data:
            console.print("❌ [error]No banks data found[/]")
            return
        
        # Create a table to display results
        table = Table(title="CONDUSEF Banks - Instituciones de Banca Múltiple")
        table.add_column("Bank Name", style="bank", no_wrap=True)
        table.add_column("Website", style="url")
        table.add_column("Additional Info", style="info")
        
        if isinstance(self.banks_data, list):
            for bank in self.banks_data:
                if isinstance(bank, dict):
                    name = bank.get('name', 'N/A')
                    website = bank.get('website', 'N/A')
                    additional = bank.get('additional_info', bank.get('contact', 'N/A'))
                    table.add_row(name, website, str(additional))
        else:
            # If it's not a list, try to display as JSON
            console.print_json(json.dumps(self.banks_data, indent=2, ensure_ascii=False))
            return
        
        console.print(table)

    def save_results(self, filename: str = None):
        """Save results to a JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"condusef_banks_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.banks_data, f, indent=2, ensure_ascii=False)
            console.print(f"💾 [success]Results saved to {filename}[/]")
        except Exception as e:
            console.print(f"❌ [error]Error saving results: {e}[/]")

    async def close(self):
        """Close the Stagehand session"""
        if self.stagehand:
            console.print("\n⏹️ [warning]Closing session...[/]")
            await self.stagehand.close()
            console.print("✅ [success]Session closed successfully![/]")

async def main():
    scraper = CONDUSEFBanksScraper()
    
    try:
        # Initialize the scraper
        await scraper.init()
        
        # Navigate to CONDUSEF
        await scraper.navigate_to_condusef()
        
        # Find the banks section
        if await scraper.find_banks_section():
            # Extract banks list
            if await scraper.extract_banks_list():
                # Try to get detailed information
                await scraper.scrape_individual_bank_pages()
                
                # Display results
                scraper.display_results()
                
                # Save results
                scraper.save_results()
            else:
                console.print("❌ [error]Failed to extract banks list[/]")
        else:
            console.print("❌ [error]Could not find banks section[/]")
            
    except Exception as e:
        console.print(f"❌ [error]Unexpected error: {e}[/]")
    finally:
        await scraper.close()

if __name__ == "__main__":
    # Add a fancy header
    console.print(
        "\n",
        Panel(
            "[light_gray]CONDUSEF Banks Scraper 🏦[/]\n"
            "[white]Extracting banking institutions from CONDUSEF SIPRES[/]",
            border_style="green",
            padding=(1, 10),
        ),
    )
    
    console.print(
        Panel(
            "[yellow]Requirements:[/]\n"
            "[white]- BROWSERBASE_API_KEY environment variable[/]\n"
            "[white]- BROWSERBASE_PROJECT_ID environment variable[/]\n"
            "[white]- MODEL_API_KEY environment variable (for Gemini)[/]",
            title="Setup Requirements",
            border_style="blue",
        )
    )
    
    asyncio.run(main())
