import asyncio
import json
import logging
import os
from datetime import datetime

from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.theme import Theme

from stagehand import Stagehand, StagehandConfig, configure_logging

# Configure logging
configure_logging(
    level=logging.INFO,
    remove_logger_name=True,
    quiet_dependencies=True,
)

# Create a custom theme
custom_theme = Theme({
    "info": "cyan",
    "success": "green", 
    "warning": "yellow",
    "error": "red bold",
    "highlight": "magenta",
    "url": "blue underline",
})

console = Console(theme=custom_theme)
load_dotenv()

async def scrape_condusef_banks():
    """Simple function to scrape CONDUSEF banks information"""
    
    # Configuration
    config = StagehandConfig(
        env="BROWSERBASE",
        api_key=os.getenv("BROWSERBASE_API_KEY"),
        project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
        headless=False,
        dom_settle_timeout_ms=5000,
        model_name="google/gemini-2.0-flash",
        self_heal=True,
        wait_for_captcha_solves=True,
        system_prompt="You are a web scraping assistant. Help navigate and extract information from the CONDUSEF website about banking institutions.",
        model_client_options={"apiKey": os.getenv("MODEL_API_KEY")},
        verbose=2,
    )
    
    stagehand = Stagehand(config)
    
    try:
        # Initialize
        console.print("\n🚀 [info]Initializing scraper...[/]")
        await stagehand.init()
        page = stagehand.page
        
        console.print(f"🌐 [white]Session URL:[/] [url]https://www.browserbase.com/sessions/{stagehand.session_id}[/]")
        
        # Navigate to CONDUSEF
        console.print("\n▶️ [highlight]Navigating to CONDUSEF...[/]")
        await page.goto("https://webapps.condusef.gob.mx/SIPRES/jsp/pub/index.jsp")
        await asyncio.sleep(5)  # Wait for page to load completely
        
        # Look for banks section
        console.print("\n▶️ [highlight]Looking for banks section...[/]")
        
        # Try different approaches to find the banks section
        try:
            # First, try to observe what's available on the page
            console.print("🔍 [info]Observing page content...[/]")
            page_content = await page.observe("find all clickable sections or categories related to banks or financial institutions")
            
            if page_content:
                console.print("✅ [success]Found page sections:[/]")
                for i, item in enumerate(page_content[:5]):  # Show first 5 items
                    console.print(f"  {i+1}. {item}")
            
            # Try to find and click on banks section
            console.print("\n▶️ [highlight]Attempting to access banks section...[/]")
            await page.act("click on 'BANCOS' or 'Instituciones de banca múltiple' or any banking-related section")
            await asyncio.sleep(3)
            
            # Extract banks information
            console.print("\n▶️ [highlight]Extracting banks information...[/]")
            banks_data = await page.extract(
                "Extract all banking institutions listed on this page. "
                "For each bank, get the name and website URL if available. "
                "Return as a structured list with 'name' and 'website' fields."
            )
            
            # Process and display results
            if banks_data:
                console.print("✅ [success]Successfully extracted banks data![/]")
                
                # Save to file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"condusef_banks_{timestamp}.json"
                
                # Convert to dict if it's a Pydantic model
                if hasattr(banks_data, 'model_dump'):
                    data_to_save = banks_data.model_dump()
                else:
                    data_to_save = banks_data
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, indent=2, ensure_ascii=False)
                
                console.print(f"💾 [success]Data saved to {filename}[/]")
                
                # Display summary
                console.print("\n📊 [info]Extracted Data Summary:[/]")
                console.print_json(json.dumps(data_to_save, indent=2, ensure_ascii=False))
                
            else:
                console.print("❌ [error]No banks data extracted[/]")
                
        except Exception as e:
            console.print(f"❌ [error]Error during scraping: {e}[/]")
            
            # Try alternative approach - extract whatever is visible
            console.print("\n🔄 [warning]Trying alternative extraction...[/]")
            try:
                alternative_data = await page.extract("extract any visible information about banks or financial institutions on this page")
                if alternative_data:
                    console.print("📋 [info]Alternative extraction result:[/]")
                    if hasattr(alternative_data, 'model_dump'):
                        console.print_json(alternative_data.model_dump_json())
                    else:
                        console.print_json(json.dumps(alternative_data, indent=2, ensure_ascii=False))
            except Exception as alt_e:
                console.print(f"❌ [error]Alternative extraction also failed: {alt_e}[/]")
        
    except Exception as e:
        console.print(f"❌ [error]Fatal error: {e}[/]")
    
    finally:
        # Close session
        console.print("\n⏹️ [warning]Closing session...[/]")
        await stagehand.close()
        console.print("✅ [success]Session closed![/]")

async def main():
    await scrape_condusef_banks()

if __name__ == "__main__":
    # Header
    console.print(
        "\n",
        Panel(
            "[light_gray]CONDUSEF Banks Scraper (Simple Version) 🏦[/]\n"
            "[white]Extracting banking institutions from CONDUSEF SIPRES[/]",
            border_style="green",
            padding=(1, 10),
        ),
    )
    
    # Check environment variables
    required_vars = ["BROWSERBASE_API_KEY", "BROWSERBASE_PROJECT_ID", "MODEL_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        console.print(
            Panel(
                f"[red]Missing required environment variables:[/]\n" +
                "\n".join([f"[white]- {var}[/]" for var in missing_vars]) +
                "\n\n[yellow]Please set these in your .env file[/]",
                title="Configuration Error",
                border_style="red",
            )
        )
    else:
        console.print(
            Panel(
                "[green]✅ All required environment variables found[/]",
                title="Configuration OK",
                border_style="green",
            )
        )
        
        asyncio.run(main())
