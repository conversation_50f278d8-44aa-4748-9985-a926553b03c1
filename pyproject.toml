[build-system]
requires = [ "setuptools>=61.0", "wheel",]
build-backend = "setuptools.build_meta"

[project]
name = "stagehand"
version = "0.5.1"
description = "Python SDK for Stagehand"
readme = "README.md"
classifiers = [ "Programming Language :: Python :: 3", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent",]
requires-python = ">=3.9"
dependencies = [ "httpx>=0.24.0", "python-dotenv>=1.0.0", "pydantic>=1.10.0", "playwright>=1.42.1", "requests>=2.31.0", "browserbase>=1.4.0", "rich>=13.7.0", "openai>=1.83.0,<1.99.6", "anthropic>=0.51.0", "litellm>=1.72.0,<1.75.0", "nest-asyncio>=1.6.0",]
[[project.authors]]
name = "Browserbase, Inc."
email = "<EMAIL>"

[project.license]
text = "MIT"

[project.optional-dependencies]
dev = [ "pytest>=7.3.1", "pytest-asyncio>=0.21.0", "pytest-mock>=3.10.0", "pytest-cov>=4.1.0", "black>=23.3.0", "isort>=5.12.0", "mypy>=1.3.0", "ruff", "psutil>=5.9.0",]

[project.urls]
Homepage = "https://github.com/browserbase/stagehand-python"
Repository = "https://github.com/browserbase/stagehand-python"

[tool.ruff]
line-length = 88
target-version = "py39"
exclude = [ ".git", ".ruff_cache", "__pycache__", "venv", ".venv", "dist", "tests",]

[tool.black]
line-length = 88
target-version = [ "py39",]
include = "\\.pyi?$"
exclude = "/(\n    \\.git\n  | \\.hg\n  | \\.mypy_cache\n  | \\.tox\n  | \\.venv\n  | _build\n  | buck-out\n  | build\n  | dist\n  | __pycache__\n  | python-sdk\n)/\n"
skip-string-normalization = false
preview = true

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_gitignore = true
skip_glob = [ "**/venv/**", "**/.venv/**", "**/__pycache__/**",]

[tool.ruff.lint]
select = [ "E", "F", "B", "C4", "UP", "N", "I", "C",]
ignore = [ "E501", "C901",]
fixable = [ "ALL",]
unfixable = []
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[tool.setuptools.package-data]
stagehand = [ "domScripts.js",]

[tool.pytest.ini_options]
testpaths = [ "tests",]
python_files = [ "test_*.py",]
python_classes = [ "Test*",]
python_functions = [ "test_*",]
asyncio_mode = "auto"
addopts = [ "--cov=stagehand", "--cov-report=html:htmlcov", "--cov-report=term-missing", "--cov-report=xml", "--strict-markers", "--strict-config", "-ra", "--tb=short",]
markers = [ "unit: Unit tests for individual components", "integration: Integration tests requiring multiple components", "e2e: End-to-end tests with full workflows", "slow: Tests that take longer to run", "browserbase: Tests requiring Browserbase connection", "local: Tests for local browser functionality", "llm: Tests involving LLM interactions", "mock: Tests using mock objects only", "performance: Performance and load tests", "smoke: Quick smoke tests for basic functionality",]
filterwarnings = [ "ignore::DeprecationWarning", "ignore::PendingDeprecationWarning", "ignore::UserWarning:pytest_asyncio", "ignore::RuntimeWarning",]
minversion = "7.0"

[tool.ruff.lint.pep8-naming]
classmethod-decorators = [ "classmethod", "validator",]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = [ "F401",]
"tests/*" = [ "F401", "F811",]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.setuptools.packages.find]
where = [ ".",]
include = [ "stagehand*",]
