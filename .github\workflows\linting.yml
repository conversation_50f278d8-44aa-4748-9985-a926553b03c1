name: Format Check

on:
  pull_request:
    branches: [ main, master ]
    paths:
      - '**.py'
      - 'stagehand/**'
      - 'pyproject.toml'
  push:
    branches: [ main, master ]
    paths:
      - '**.py'
      - 'stagehand/**'
      - 'pyproject.toml'

jobs:
  format-check:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
          
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install formatting dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black ruff
          
      - name: Check Black formatting
        run: |
          echo "Checking Black formatting..."
          black --check --diff stagehand
          
      - name: Run Ruff linting
        run: |
          echo "Running Ruff linting..."
          ruff check stagehand 