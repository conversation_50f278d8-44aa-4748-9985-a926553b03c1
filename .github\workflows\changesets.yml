name: Changesets

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  changesets:
    name: Create or Update Release PR
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install uv
        uses: astral-sh/setup-uv@v2

      - name: Check for changesets
        id: check_changesets
        run: |
          if ls .changeset/*.md 2>/dev/null | grep -v README.md > /dev/null; then
            echo "has_changesets=true" >> $GITHUB_OUTPUT
          else
            echo "has_changesets=false" >> $GITHUB_OUTPUT
          fi

      - name: Get PR metadata
        if: steps.check_changesets.outputs.has_changesets == 'true'
        id: pr_metadata
        run: |
          # Get the merge commit info
          COMMIT_SHA="${{ github.sha }}"
          echo "COMMIT_SHA=$COMMIT_SHA" >> $GITHUB_ENV

          # Try to extract PR info from commit message
          PR_NUMBER=$(git log -1 --pretty=%B | grep -oP '(?<=#)\d+' | head -1 || echo "")
          if [ -n "$PR_NUMBER" ]; then
            echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV

            # Get PR author using GitHub API
            PR_AUTHOR=$(gh api repos/${{ github.repository }}/pulls/$PR_NUMBER --jq '.user.login' || echo "")
            echo "PR_AUTHOR=$PR_AUTHOR" >> $GITHUB_ENV
          fi
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate changelogs and PR description
        if: steps.check_changesets.outputs.has_changesets == 'true'
        run: |
          # Generate changelogs and PR description
          uvx changeset changelog --output-pr-description pr-description.md

          # Save PR description for later use
          echo "PR_DESCRIPTION<<EOF" >> $GITHUB_ENV
          cat pr-description.md >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          rm pr-description.md
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Bump versions
        if: steps.check_changesets.outputs.has_changesets == 'true'
        run: |
          uvx changeset version --skip-changelog

      - name: Commit changes
        if: steps.check_changesets.outputs.has_changesets == 'true'
        id: commit
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          # Add all changes
          git add .

          # Commit if there are changes
          if ! git diff --cached --quiet; then
            git commit -m "Version packages and update changelogs"
            echo "has_changes=true" >> $GITHUB_OUTPUT
          else
            echo "has_changes=false" >> $GITHUB_OUTPUT
          fi

      - name: Force push to changeset branch
        if: steps.check_changesets.outputs.has_changesets == 'true' && steps.commit.outputs.has_changes == 'true'
        run: |
          # Force push to the changeset-release branch
          git push origin HEAD:changeset-release --force

      - name: Create or update PR
        if: steps.check_changesets.outputs.has_changesets == 'true' && steps.commit.outputs.has_changes == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              head: `${context.repo.owner}:changeset-release`,
              base: 'main',
              state: 'open'
            });

            const prBody = process.env.PR_DESCRIPTION;
            const prTitle = '🚀 Release packages';

            if (prs.length > 0) {
              // Update existing PR
              const pr = prs[0];
              await github.rest.pulls.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: pr.number,
                title: prTitle,
                body: prBody
              });
              console.log(`Updated PR #${pr.number}`);
            } else {
              // Create new PR
              const { data: pr } = await github.rest.pulls.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: prTitle,
                body: prBody,
                head: 'changeset-release',
                base: 'main'
              });
              console.log(`Created PR #${pr.number}`);
            }
