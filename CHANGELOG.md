# Stagehand Python Changelog

## 0.5.1

### Patch Changes

[#183](https://github.com/browserbase/stagehand-python/pull/183) [`6f72281`](https://github.com/browserbase/stagehand-python/commit/6f72281) Thanks @shubh24 and @miguelg719! - Fixing downloads behavior for use_api=false
[#132](https://github.com/browserbase/stagehand-python/pull/132) [`edc57ac`](https://github.com/browserbase/stagehand-python/commit/edc57ac) Thanks @sanveer-osahan and @miguelg719! - Add LLM customization support (eg. api_base)
[#179](https://github.com/browserbase/stagehand-python/pull/179) [`51ca053`](https://github.com/browserbase/stagehand-python/commit/51ca053) Thanks @miguelg719! - Fix max_steps parsing for agent execute options
[#176](https://github.com/browserbase/stagehand-python/pull/176) [`d95974a`](https://github.com/browserbase/stagehand-python/commit/d95974a) Thanks @miguelg719! - Fix stagehand.metrics on env:BROWSERBASE
[#88](https://github.com/browserbase/stagehand-python/pull/88) [`021c946`](https://github.com/browserbase/stagehand-python/commit/021c946) Thanks @filip-michalsky! - added regression tests
[#161](https://github.com/browserbase/stagehand-python/pull/161) [`f68e86c`](https://github.com/browserbase/stagehand-python/commit/f68e86c) Thanks @arunpatro, @miguelg719 and Filip Michalsky! - Multi-tab support
[#181](https://github.com/browserbase/stagehand-python/pull/181) [`1bef512`](https://github.com/browserbase/stagehand-python/commit/1bef512) Thanks @miguelg719! - Fix openai-litellm dependency bug
[#177](https://github.com/browserbase/stagehand-python/pull/177) [`36ba981`](https://github.com/browserbase/stagehand-python/commit/36ba981) Thanks @miguelg719! - Fix temperature setting for GPT-5 family of models
[#174](https://github.com/browserbase/stagehand-python/pull/174) [`2e3eb1a`](https://github.com/browserbase/stagehand-python/commit/2e3eb1a) Thanks @miguelg719! - Added frame_id_map to support multi-tab handling on API

## 0.5.0

### Minor Changes
[#167](https://github.com/browserbase/stagehand-python/pull/167) [`76669f0`](https://github.com/browserbase/stagehand-python/commit/76669f0) Thanks @miguelg719! - Enable access to iframes on api

### Patch Changes

[#168](https://github.com/browserbase/stagehand-python/pull/168) [`a7d8c5e`](https://github.com/browserbase/stagehand-python/commit/a7d8c5e) Thanks @miguelg719! - Patch issue with passing a created session_id to init on api mode
[#155](https://github.com/browserbase/stagehand-python/pull/155) [`8d55709`](https://github.com/browserbase/stagehand-python/commit/8d55709) Thanks @Zach10za! - Fix error in press_key act util function
[#158](https://github.com/browserbase/stagehand-python/pull/158) [`426df10`](https://github.com/browserbase/stagehand-python/commit/426df10) Thanks @miguelg719! - Fix parsing schema for extract with no arguments (full page extract)
[#166](https://github.com/browserbase/stagehand-python/pull/166) [`15fd40b`](https://github.com/browserbase/stagehand-python/commit/15fd40b) Thanks @filip-michalsky! - fix logging param name
[#159](https://github.com/browserbase/stagehand-python/pull/159) [`cd3dc7f`](https://github.com/browserbase/stagehand-python/commit/cd3dc7f) Thanks @tkattkat! - Add support for claude 4 sonnet in agent & remove all images but the last two from anthropic cua client

## 0.4.1

### Patch Changes

[#146](https://github.com/browserbase/stagehand-python/pull/146) [`d0983da`](https://github.com/browserbase/stagehand-python/commit/d0983da) Thanks @miguelg719 and @the-roaring! - Pass sdk version number to API for debugging
[#145](https://github.com/browserbase/stagehand-python/pull/145) [`0732268`](https://github.com/browserbase/stagehand-python/commit/0732268) Thanks @filip-michalsky! - relaxed rich to 13.7.0
[#152](https://github.com/browserbase/stagehand-python/pull/152) [`f888d81`](https://github.com/browserbase/stagehand-python/commit/f888d81) Thanks @filip-michalsky! - simple event loop timeout for strict event loops for async playwright (which has blocking start)
[#140](https://github.com/browserbase/stagehand-python/pull/140) [`5e70c8d`](https://github.com/browserbase/stagehand-python/commit/5e70c8d) Thanks @Zach10za! - Add support for handling OS-level dropdowns

## 0.4.0

### Minor Changes

[#127](https://github.com/browserbase/stagehand-python/pull/127) [`a2fee2c`](https://github.com/browserbase/stagehand-python/commit/a2fee2c) Thanks @the-roaring! - bump to unused version range

## 0.1.0

### Minor Changes

[#126](https://github.com/browserbase/stagehand-python/pull/126) [`5263553`](https://github.com/browserbase/stagehand-python/commit/5263553) Thanks @the-roaring! - bump minor version to fix publishing disparity

### Patch Changes

[#126](https://github.com/browserbase/stagehand-python/pull/126) [`5263553`](https://github.com/browserbase/stagehand-python/commit/5263553) Thanks @the-roaring! - start using pychangeset to track changes
