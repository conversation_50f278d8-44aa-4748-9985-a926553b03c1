{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install stagehand"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["import os\n", "from pprint import pprint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "dotenv.load_dotenv()\n"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["from stagehand import Stagehand, StagehandConfig\n", "\n", "stagehand = Stagehand(StagehandConfig(\n", "    env=\"BROWSERBASE\",\n", "    model_name=\"openai/gpt-4o-mini\",\n", "    api_key=os.getenv(\"BROWSERBASE_API_KEY\"),\n", "    project_id=os.getenv(\"BROWSERBASE_PROJECT_ID\"),\n", "    api_url=\"https://api.stagehand.browserbase.com/v1\",\n", "    # headless=False,\n", "    verbose=0,\n", "    # dom_settle_timeout_ms=3000,\n", "), model_api_key=os.getenv(\"OPENAI_API_KEY\"))"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["await stagehand.init()"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["page = stagehand.page\n", "await page.goto(\"https://www.hackernews.com\")"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "class Post(BaseModel):\n", "    title: str\n", "    points: int\n", "    comments: int\n", "\n", "class Posts(BaseModel):\n", "    posts: List[Post]\n", "\n", "res = await page.extract(\"find all the posts related to the topic 'AI'\", schema=Posts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pprint(res.posts)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["await stagehand.close()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 2}