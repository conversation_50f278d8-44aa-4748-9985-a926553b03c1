# CONDUSEF Banks Scraper 🏦

Esta herramienta utiliza Stagehand para extraer información de bancos e instituciones de banca múltiple del sitio web de CONDUSEF (Comisión Nacional para la Protección y Defensa de los Usuarios de Servicios Financieros).

## 🎯 Objetivo

Extraer automáticamente los sitios web y información de contacto de todos los bancos listados en la sección "BANCOS - Instituciones de banca múltiple" del portal SIPRES de CONDUSEF.

## 📋 Requisitos Previos

### 1. Variables de Entorno

Crea un archivo `.env` en el directorio raíz con las siguientes variables:

```env
BROWSERBASE_API_KEY=tu_api_key_de_browserbase
BROWSERBASE_PROJECT_ID=tu_project_id_de_browserbase
MODEL_API_KEY=tu_api_key_de_gemini
```

### 2. Cuentas Necesarias

- **Browserbase**: Para automatización del navegador
  - Regístrate en [browserbase.com](https://browserbase.com)
  - Obtén tu API key y Project ID

- **Google AI (Gemini)**: Para el modelo de IA
  - Obtén tu API key en [Google AI Studio](https://aistudio.google.com)

### 3. Dependencias

Las dependencias ya están incluidas en el proyecto Stagehand:
- `stagehand` - Framework de automatización web
- `rich` - Para output formateado en consola
- `python-dotenv` - Para manejo de variables de entorno

## 🚀 Uso

### Opción 1: Scraper Completo (Recomendado)

```bash
python condusef_banks_scraper.py
```

Este script incluye:
- ✅ Navegación automática al sitio de CONDUSEF
- ✅ Búsqueda de la sección de bancos
- ✅ Extracción de información básica
- ✅ Navegación a páginas individuales para detalles
- ✅ Guardado automático en JSON
- ✅ Visualización en tabla formateada

### Opción 2: Scraper Simple

```bash
python condusef_banks_simple.py
```

Este script es más directo:
- ✅ Navegación básica
- ✅ Extracción simple de datos
- ✅ Manejo de errores robusto
- ✅ Guardado en JSON

## 📊 Salida de Datos

### Formato JSON

Los datos se guardan en archivos JSON con el formato:

```json
[
  {
    "name": "Nombre del Banco",
    "website": "https://www.banco.com",
    "additional_info": "Información adicional de contacto"
  }
]
```

### Archivos Generados

- `condusef_banks_YYYYMMDD_HHMMSS.json` - Datos extraídos con timestamp

## 🔧 Configuración Avanzada

### Modificar el Modelo de IA

En el código, puedes cambiar el modelo:

```python
model_name="google/gemini-2.0-flash"  # Actual
# model_name="openai/gpt-4"           # Alternativa
```

### Ajustar Timeouts

```python
dom_settle_timeout_ms=5000  # Tiempo de espera para DOM
```

### Modo Headless

```python
headless=False  # Ver el navegador (recomendado para debugging)
headless=True   # Modo invisible (más rápido)
```

## 🐛 Solución de Problemas

### Error: "Missing environment variables"

**Solución**: Verifica que tu archivo `.env` contenga todas las variables requeridas.

### Error: "Could not find banks section"

**Posibles causas**:
- El sitio web cambió su estructura
- Problemas de conectividad
- El sitio requiere interacción adicional (captcha, etc.)

**Solución**: 
1. Ejecuta con `headless=False` para ver qué está pasando
2. Verifica manualmente que el sitio esté accesible
3. Ajusta los selectores en el código si es necesario

### Error: "Session timeout"

**Solución**: 
- Aumenta `dom_settle_timeout_ms`
- Verifica tu conexión a internet
- Revisa los límites de tu cuenta Browserbase

## 📝 Personalización

### Cambiar el Sitio Objetivo

Para usar con otros sitios de CONDUSEF:

```python
await page.goto("https://webapps.condusef.gob.mx/SIPRES/jsp/pub/index.jsp")
```

### Modificar Criterios de Extracción

Ajusta los prompts de extracción:

```python
banks_data = await page.extract(
    "Tu prompt personalizado para extraer información específica"
)
```

## 🔍 Monitoreo

### Ver Sesión en Vivo

Cuando ejecutes el script, verás una URL como:
```
🌐 View your live browser: https://www.browserbase.com/sessions/[session_id]
```

Puedes abrir esta URL para ver en tiempo real lo que está haciendo el scraper.

### Logs Detallados

Ajusta el nivel de verbosidad:

```python
verbose=1  # Mínimo
verbose=2  # Medio (recomendado)
verbose=3  # Detallado (debug)
```

## ⚖️ Consideraciones Legales

- ✅ Respeta los términos de uso del sitio web
- ✅ No hagas scraping excesivo que pueda sobrecargar el servidor
- ✅ Los datos extraídos son públicos y están disponibles en el sitio oficial
- ✅ Usa la información de manera responsable

## 🤝 Contribuciones

Para mejorar el scraper:

1. Fork el repositorio
2. Crea una rama para tu feature
3. Implementa mejoras
4. Envía un pull request

## 📞 Soporte

Si encuentras problemas:

1. Revisa los logs detallados
2. Verifica la configuración
3. Consulta la documentación de Stagehand
4. Abre un issue en el repositorio

---

**Nota**: Este scraper está diseñado específicamente para el sitio de CONDUSEF. Si el sitio cambia su estructura, puede requerir actualizaciones en el código.
