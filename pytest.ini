[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto

markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    smoke: marks tests as smoke tests
    local: marks tests as local integration tests
    api: marks tests as API integration tests
    e2e: marks tests as end-to-end tests
    regression: marks tests as regression tests

log_cli = true
log_cli_level = INFO 