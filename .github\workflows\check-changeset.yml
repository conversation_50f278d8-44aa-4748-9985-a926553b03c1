name: Check Changeset

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  check-changeset:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install uv
        uses: astral-sh/setup-uv@v2

      - name: Check for changeset
        run: |
          uvx changeset check-changeset