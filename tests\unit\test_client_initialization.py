import asyncio
import unittest.mock as mock
import os

import pytest

from stagehand import Stagehand
from stagehand.config import StagehandConfig


class TestClientInitialization:
    """Tests for the Stagehand client initialization and configuration."""

    @pytest.mark.smoke
    @mock.patch.dict(os.environ, {}, clear=True)
    def test_init_with_direct_params(self):
        """Test initialization with direct parameters."""
        # Create a config with LOCAL env to avoid BROWSERBASE validation issues
        config = StagehandConfig(env="LOCAL")
        client = Stagehand(
            config=config,
            api_url="http://test-server.com",
            browserbase_session_id="test-session",
            api_key="test-api-key",
            project_id="test-project-id",
            model_api_key="test-model-api-key",
            verbose=2,
        )

        assert client.api_url == "http://test-server.com"
        assert client.session_id == "test-session"
        # In LOCAL mode, browserbase keys are not used
        assert client.model_api_key == "test-model-api-key"
        assert client.verbose == 2
        assert client._initialized is False
        assert client._closed is False

    @pytest.mark.smoke
    @mock.patch.dict(os.environ, {}, clear=True)
    def test_init_with_config(self):
        """Test initialization with a configuration object."""
        config = StagehandConfig(
            env="LOCAL",  # Use LOCAL to avoid BROWSERBASE validation
            api_key="config-api-key",
            project_id="config-project-id",
            browserbase_session_id="config-session-id",
            model_name="gpt-4",
            dom_settle_timeout_ms=500,
            self_heal=True,
            wait_for_captcha_solves=True,
            system_prompt="Custom system prompt for testing",
        )

        client = Stagehand(config=config, api_url="http://test-server.com")

        assert client.api_url == "http://test-server.com"
        assert client.session_id == "config-session-id"
        assert client.browserbase_api_key == "config-api-key"
        assert client.browserbase_project_id == "config-project-id"
        assert client.model_name == "gpt-4"
        assert client.dom_settle_timeout_ms == 500
        assert hasattr(client, "self_heal")
        assert client.self_heal is True
        assert hasattr(client, "wait_for_captcha_solves")
        assert client.wait_for_captcha_solves is True
        assert hasattr(client, "config")
        assert hasattr(client, "system_prompt")
        assert client.system_prompt == "Custom system prompt for testing"

    @mock.patch.dict(os.environ, {}, clear=True)
    def test_config_priority_over_direct_params(self):
        """Test that config parameters take precedence over direct parameters (except session_id)."""
        config = StagehandConfig(
            env="LOCAL",  # Use LOCAL to avoid BROWSERBASE validation
            api_key="config-api-key",
            project_id="config-project-id",
            browserbase_session_id="config-session-id",
        )

        client = Stagehand(
            config=config,
            api_key="direct-api-key",
            project_id="direct-project-id",
            browserbase_session_id="direct-session-id",
        )

        # Override parameters take precedence over config parameters
        assert client.browserbase_api_key == "direct-api-key"
        assert client.browserbase_project_id == "direct-project-id"
        # session_id parameter overrides config since it's passed as browserbase_session_id override
        assert client.session_id == "direct-session-id"

    def test_init_with_missing_required_fields(self):
        """Test initialization with missing required fields."""
        # No error when initialized without session_id
        client = Stagehand(
            api_key="test-api-key", project_id="test-project-id"
        )
        assert client.session_id is None

        # Test that error handling for missing API key is functioning
        # by patching the ValueError that should be raised
        with mock.patch.object(
            Stagehand,
            "__init__",
            side_effect=ValueError("browserbase_api_key is required"),
        ):
            with pytest.raises(ValueError, match="browserbase_api_key is required"):
                Stagehand(
                    browserbase_session_id="test-session", project_id="test-project-id"
                )

    def test_init_as_context_manager(self):
        """Test the client as a context manager."""
        client = Stagehand(
            api_url="http://test-server.com",
            browserbase_session_id="test-session",
            api_key="test-api-key",
            project_id="test-project-id",
        )

        # Mock the async context manager methods
        client.__aenter__ = mock.AsyncMock(return_value=client)
        client.__aexit__ = mock.AsyncMock()
        client.init = mock.AsyncMock()
        client.close = mock.AsyncMock()

        # We can't easily test an async context manager in a non-async test,
        # so we just verify the methods exist and are async
        assert hasattr(client, "__aenter__")
        assert hasattr(client, "__aexit__")

        # Verify init is called in __aenter__
        assert client.init is not None

        # Verify close is called in __aexit__
        assert client.close is not None

    @pytest.mark.asyncio
    async def test_init_playwright_timeout(self):
        """Test that init() raises TimeoutError when playwright takes too long to start."""
        config = StagehandConfig(env="LOCAL")
        client = Stagehand(config=config)

        # Mock async_playwright to simulate a hanging start() method
        mock_playwright_instance = mock.AsyncMock()
        mock_start = mock.AsyncMock()
        
        # Make start() hang indefinitely
        async def hanging_start():
            await asyncio.sleep(100)  # Sleep longer than the 30s timeout
        
        mock_start.side_effect = hanging_start
        mock_playwright_instance.start = mock_start

        with mock.patch("stagehand.main.async_playwright", return_value=mock_playwright_instance):
            # The init() method should raise TimeoutError due to the 30-second timeout
            with pytest.raises(asyncio.TimeoutError):
                await client.init()

        # Ensure the client is not marked as initialized
        assert client._initialized is False

    @pytest.mark.asyncio
    async def test_create_session(self):
        """Test session creation."""
        client = Stagehand(
            api_url="http://test-server.com",
            api_key="test-api-key",
            project_id="test-project-id",
            model_api_key="test-model-api-key",
        )

        # Override the _create_session method for easier testing
        original_create_session = client._create_session

        async def mock_create_session():
            client.session_id = "new-test-session-id"

        client._create_session = mock_create_session

        # Call _create_session
        await client._create_session()

        # Verify session ID was set
        assert client.session_id == "new-test-session-id"

    @pytest.mark.asyncio
    async def test_create_session_failure(self):
        """Test session creation failure."""
        client = Stagehand(
            api_url="http://test-server.com",
            api_key="test-api-key",
            project_id="test-project-id",
            model_api_key="test-model-api-key",
        )

        # Override the _create_session method to raise an error
        original_create_session = client._create_session

        async def mock_create_session():
            raise RuntimeError("Failed to create session: Invalid request")

        client._create_session = mock_create_session

        # Call _create_session and expect error
        with pytest.raises(RuntimeError, match="Failed to create session"):
            await client._create_session()

    @pytest.mark.asyncio
    async def test_create_session_invalid_response(self):
        """Test session creation with invalid response format."""
        client = Stagehand(
            api_url="http://test-server.com",
            api_key="test-api-key",
            project_id="test-project-id",
            model_api_key="test-model-api-key",
        )

        # Override the _create_session method to raise a specific error
        original_create_session = client._create_session

        async def mock_create_session():
            raise RuntimeError("Invalid response format: {'success': true, 'data': {}}")

        client._create_session = mock_create_session

        # Call _create_session and expect error
        with pytest.raises(RuntimeError, match="Invalid response format"):
            await client._create_session()

    @mock.patch.dict(os.environ, {"MODEL_API_KEY": "test-model-api-key"}, clear=True)
    def test_init_with_model_api_key_in_env(self):
        config = StagehandConfig(env="LOCAL")
        client = Stagehand(config=config)
        assert client.model_api_key == "test-model-api-key"

    def test_init_with_custom_llm(self):
        config = StagehandConfig(
            env="LOCAL",
            model_client_options={"apiKey": "custom-llm-key", "baseURL": "https://custom-llm.com"}
        )
        client = Stagehand(config=config)
        assert client.model_api_key == "custom-llm-key"
        assert client.model_client_options["apiKey"] == "custom-llm-key"
        assert client.model_client_options["baseURL"] == "https://custom-llm.com"

    def test_init_with_custom_llm_override(self):
        config = StagehandConfig(
            env="LOCAL",
            model_client_options={"apiKey": "custom-llm-key", "baseURL": "https://custom-llm.com"}
        )
        client = Stagehand(
            config=config,
            model_client_options={"apiKey": "override-llm-key", "baseURL": "https://override-llm.com"}
        )
        assert client.model_api_key == "override-llm-key"
        assert client.model_client_options["apiKey"] == "override-llm-key"
        assert client.model_client_options["baseURL"] == "https://override-llm.com"